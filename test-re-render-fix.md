# useCertificateFiles Re-render Fix Test

## Root Cause Identified

The excessive re-renders in `useCertificateFiles` hook were caused by:

1. **Flawed useMemo implementation** in line 17 of `useCertificateFiles.ts`:
   ```typescript
   const prevCertificateIdRef = useMemo(() => ({ current: certificateId }), []);
   ```
   - Empty dependency array `[]` meant the memoized value never updated
   - This broke the change detection logic in the subsequent useEffect

2. **Multiple hook instances**: VerbrauchPage renders 3 DirectoryBasedFileUpload components, each calling the hook

3. **Aggressive query settings**: staleTime: 0 and refetchOnMount: true caused frequent re-renders

4. **Missing prop memoization**: activeCertificateId wasn't memoized, causing child re-renders

## Fixes Applied

### 1. Fixed the change detection logic
- Replaced `useMemo` with `useRef` for proper previous value tracking
- Added proper import for `useRef`

### 2. Added prop memoization in VerbrauchPage
- Memoized `activeCertificateId` to prevent unnecessary child re-renders
- Updated all DirectoryBasedFileUpload components to use memoized value

### 3. Optimized query settings
- Increased staleTime from 0 to 5 minutes
- Disabled refetchOnWindowFocus and refetchOnMount

## Expected Results

- The console log "📁 useCertificateFiles hook called with: [id]" should appear much less frequently
- The hook should only log when certificateId actually changes, not on every component re-render
- Overall performance should improve on the /verbrauch route

## Test Steps

1. Navigate to /erfassen/verbrauch
2. Check browser console for reduced logging
3. Interact with form fields and file uploads
4. Verify hook is only called when necessary
