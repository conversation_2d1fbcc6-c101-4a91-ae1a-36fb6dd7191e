import { RouterProvider } from '@tanstack/react-router';
import { router } from './routes';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { CertificateProvider } from './contexts/CertificateContext';
import { useEffect, useMemo } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create a client
const queryClient = new QueryClient();

function AppWithAuth() {
  const { user, session, loading } = useAuth();

  // Memoize router context to prevent unnecessary updates
  const routerContext = useMemo(() => ({
    user,
    session,
    loading,
  }), [user?.id, session?.access_token, loading]); // More specific dependencies

  useEffect(() => {
    // Only update the router context when authentication succeeds or when the component mounts
    // This prevents router updates on failed login attempts
    if (!loading) { // Only update when loading is complete
      console.log('Updating router context with:', routerContext);
      router.update({
        context: routerContext,
      });
    }
  }, [routerContext, loading]);

    return (
      <>
        {loading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="text-center bg-white p-6 rounded-lg">
              <div className="w-16 h-16 border-t-4 border-green-500 border-solid rounded-full animate-spin mx-auto"></div>
              <p className="mt-4 text-gray-600">Credentials werden geprüft...</p>
            </div>
          </div>
        )}
        <RouterProvider router={router} />
      </>
    );
    
}

function App() {
  return (
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <CertificateProvider>
          <AppWithAuth />
        </CertificateProvider>
      </QueryClientProvider>
    </AuthProvider>
  );
}

export default App;
