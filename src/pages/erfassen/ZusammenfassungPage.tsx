import { useState, useEffect, useMemo, memo, type FC, type ReactNode } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../../lib/supabase';
import { useNavigate, Link } from '@tanstack/react-router';
import { useCertificate } from '../../contexts/CertificateContext';
import { useAuth } from '../../contexts/AuthContext';
import { type EnergieausweisData } from '../../types/csv';
import { FileWithSignedUrl } from '../../components/ui/FileWithSignedUrl';
import { useCertificateFiles } from '../../hooks/useCertificateFiles';
import { Breadcrumb } from '../../components/ui/Breadcrumb';
import { usePageVisit } from '../../hooks/usePageVisit';
import { AccountConversionModal } from '../../components/auth/AccountConversionModal';
import { getUserEmailFromCertificate } from '../../utils/accountConversion';
import { AnonymousUserIndicator } from '../../components/ui/AnonymousUserIndicator';
import { PricingService } from '../../services/pricingService';

// Define specific error types for better error handling
interface CheckoutError {
  message: string;
  code?: string;
}

// Field label mappings - maps technical field names to user-friendly labels
const fieldLabels: Record<string, string> = {
  // Objektdaten
  Strasse: 'Straße',
  Hausnr: 'Hausnummer',
  PLZ: 'PLZ',
  Ort: 'Ort',
  WSchVo77_erfuellt: 'Wärmeschutzverordnung 1977 erfüllt',
  gebaeudebild: 'Gebäudebild',

  // Kundendaten
  Kunden_Anrede: 'Anrede',
  Kunden_Vorname: 'Vorname',
  Kunden_Nachname: 'Nachname',
  Kunden_Strasse: 'Straße',
  Kunden_Hausnr: 'Hausnummer',
  Kunden_PLZ: 'PLZ',
  Kunden_Ort: 'Ort',
  Kunden_email: 'E-Mail',
  Kunden_telefon: 'Telefon',

  // Gebäudedetails 1
  BedarfVerbrauch: 'Bedarf/Verbrauch',
  Anlass: 'Anlass der Ausstellung',
  Datenerhebung: 'Datenerhebung durch',
  nichtWohnGeb: 'Gebäudetyp',
  isGebaeudehuelle: 'Gebäudehülle',
  Nutzung1_ID: 'Nutzung1 ID',
  Nutzung1_Flaeche: 'Nutzung1 Fläche (m²)',
  Baujahr: 'Baujahr',
  Modernisierung: 'Jahr der Modernisierung',
  Wohnfläche: 'Wohnfläche (m²)',
  Raumhöhe: 'Raumhöhe (m)',
  Volumen: 'Gebäudevolumen (m³)',
  Wohneinheiten: 'Anzahl Wohneinheiten',
  Geschosse: 'Anzahl Geschosse',
  anbauSituation: 'Anbausituation',
  Keller_beheizt: 'Keller beheizt',
  Klimatisiert: 'Klimatisiert',
  ergaenzendeErlaeuterungen: 'Ergänzende Erläuterungen',
  baujahrHzErz: 'Baujahr Heizung',

  // Gebäudedetails 2
  Gebaeudeart: 'Gebäudeart',
  Gebaeudekategorie: 'Gebäudekategorie',
  Gebaeudetyp: 'Gebäudetyp',
  Gebaeudeform: 'Gebäudeform',
  Dachform: 'Dachform',
  Dachgeschossausbau: 'Dachgeschossausbau',
  Kellergeschoss: 'Kellergeschoss',
  kuehlWfl: 'Kühlfläche (m²)',
  Originaldaemmstandard: 'Originaldämmstandard',
  bjFensterAustausch: 'Jahr des Fensteraustauschs',
  Fensterlüftung: 'Fensterlüftung',
  Schachtlüftung: 'Schachtlüftung',
  L_Mit_WRG: 'Lüftungsanlage mit Wärmerückgewinnung',
  L_Ohne_WRG: 'Lüftungsanlage ohne Wärmerückgewinnung',

  // Bauteile
  boeden: 'Böden',
  daecher: 'Dächer',
  waende: 'Wände',
  bezeichnung: 'Bezeichnung',
  massiv: 'Material',
  uebergang: 'Übergang',
  flaeche: 'Fläche (m²)',
  // uWert: 'U-Wert (W/m²K)', // Removed from UI display
  daemmung: 'Dämmung (cm)',

  // Böden (dynamisch)
  Boden1: 'Boden 1 - Bezeichnung',
  Boden1_massiv: 'Boden 1 - Material',
  Boden1_Kellerdecke: 'Boden 1 - Übergang zu unbeheiztem Keller',
  Boden1_Fläche: 'Boden 1 - Fläche (m²)',
  // Boden1_U_Wert: 'Boden 1 - U-Wert (W/m²K)', // Removed from UI display
  Boden1_Dämmung: 'Boden 1 - Dämmung (cm)',

  Boden2: 'Boden 2 - Bezeichnung',
  Boden2_massiv: 'Boden 2 - Material',
  Boden2_Kellerdecke: 'Boden 2 - Übergang zu unbeheiztem Keller',
  Boden2_Fläche: 'Boden 2 - Fläche (m²)',
  // Boden2_U_Wert: 'Boden 2 - U-Wert (W/m²K)', // Removed from UI display
  Boden2_Dämmung: 'Boden 2 - Dämmung (cm)',

  // Dächer (dynamisch)
  Dach1: 'Dach 1 - Bezeichnung',
  Dach1_massiv: 'Dach 1 - Material',
  Dach1_Geschossdecke: 'Dach 1 - Übergang zu unbeheiztem Dachraum',
  Dach1_Fläche: 'Dach 1 - Fläche (m²)',
  // Dach1_U_Wert: 'Dach 1 - U-Wert (W/m²K)', // Removed from UI display
  Dach1_Dämmung: 'Dach 1 - Dämmung (cm)',

  Dach2: 'Dach 2 - Bezeichnung',
  Dach2_massiv: 'Dach 2 - Material',
  Dach2_Geschossdecke: 'Dach 2 - Übergang zu unbeheiztem Dachraum',
  Dach2_Fläche: 'Dach 2 - Fläche (m²)',
  // Dach2_U_Wert: 'Dach 2 - U-Wert (W/m²K)', // Removed from UI display
  Dach2_Dämmung: 'Dach 2 - Dämmung (cm)',

  // Wände (dynamisch)
  Wand1: 'Wand 1 - Bezeichnung',
  Wand1_massiv: 'Wand 1 - Material',
  Wand1_Fläche: 'Wand 1 - Fläche (m²)',
  // Wand1_U_Wert: 'Wand 1 - U-Wert (W/m²K)', // Removed from UI display
  Wand1_Dämmung: 'Wand 1 - Dämmung (cm)',

  Wand2: 'Wand 2 - Bezeichnung',
  Wand2_massiv: 'Wand 2 - Material',
  Wand2_Fläche: 'Wand 2 - Fläche (m²)',
  // Wand2_U_Wert: 'Wand 2 - U-Wert (W/m²K)', // Removed from UI display
  Wand2_Dämmung: 'Wand 2 - Dämmung (cm)',

  // Fenster
  Fensterart: 'Fensterart',
  Verglasung: 'Verglasung',
  Rahmen: 'Rahmen',
  // Fenster_U_Wert: 'U-Wert Fenster', // Removed from UI display

  // Dynamische Fenster
  Fenster1_bezeichnung: 'Fenster 1 - Bezeichnung',
  Fenster1_art: 'Fenster 1 - Art',
  Fenster1_flaeche: 'Fenster 1 - Fläche (m²)',
  // Fenster1_uWert: 'Fenster 1 - U-Wert (W/m²K)', // Removed from UI display
  Fenster1_ausrichtung: 'Fenster 1 - Ausrichtung',
  Fenster1_baujahr: 'Fenster 1 - Baujahr',

  Fenster2_bezeichnung: 'Fenster 2 - Bezeichnung',
  Fenster2_art: 'Fenster 2 - Art',
  Fenster2_flaeche: 'Fenster 2 - Fläche (m²)',
  // Fenster2_uWert: 'Fenster 2 - U-Wert (W/m²K)', // Removed from UI display
  Fenster2_ausrichtung: 'Fenster 2 - Ausrichtung',
  Fenster2_baujahr: 'Fenster 2 - Baujahr',

  Fenster3_bezeichnung: 'Fenster 3 - Bezeichnung',
  Fenster3_art: 'Fenster 3 - Art',
  Fenster3_flaeche: 'Fenster 3 - Fläche (m²)',
  // Fenster3_uWert: 'Fenster 3 - U-Wert (W/m²K)', // Removed from UI display
  Fenster3_ausrichtung: 'Fenster 3 - Ausrichtung',
  Fenster3_baujahr: 'Fenster 3 - Baujahr',

  // Heizung
  Hzg_Speicher_Baujahr: 'Baujahr Pufferspeicher',
  Hzg_Verteilung_Baujahr: 'Baujahr Verteilleitungen',
  Hzg_Übergabe: 'Wärmeübergabe',
  Hzg_Verteilung_Art: 'Art der Wärmeverteilung',
  Hzg_kreistemperatur: 'Heizkreistemperatur',
  Hzg_Verteilung_Dämmung: 'Dämmung der Verteilleitungen',
  Hzg_Speicher: 'Pufferspeicher vorhanden',
  Hzg_Aufstellung: 'Aufstellungsort der Heizung',
  Hzg_Technik: 'Heiztechnik',
  Hzg_Energieträger: 'Energieträger',
  Hzg_PrimFaktor: 'Primärenergiefaktor',

  // Trinkwarmwasser & Lüftung
  TW_Baujahr: 'Baujahr der Trinkwarmwasseranlage',
  TW_Speicher_Baujahr: 'Baujahr des TW-Speichers',
  TW_Verteilung_Baujahr: 'Baujahr der TW-Leitungen',
  TW_Verteilung_Art: 'Art der TW-Verteilung',
  TW_Verteilung_Dämmung: 'Dämmung der TW-Leitungen',
  TW_Zirkulation: 'Trinkwasser-Zirkulation',
  TW_Speicher_Standort: 'Standort des TW-Speichers',
  TW_Technik: 'Technik der Trinkwarmwasserbereitung',
  TW_Solar: 'Solaranlage für Trinkwarmwasser',
  TW_WP: 'Wärmepumpe für Trinkwarmwasser',
  HZ_Solar: 'Solaranlage für Heizung',
  HZ_WP: 'Wärmepumpe für Heizung',
  Luft_Baujahr: 'Baujahr der Lüftungsanlage',
  Luft_Verteilung_Baujahr: 'Baujahr der Luftleitungen',
  Luft_Lage: 'Lage der Lüftungsanlage',
  Luft_Typ: 'Typ der Lüftungsanlage',
  Lueftungskonzept: 'Lüftungskonzept',
  Lueftungsanlage: 'Lüftungsanlage',

  // Verbrauchsdaten
  Abrechnungszeitraum: 'Abrechnungszeitraum',
  Verbrauch_Heizung: 'Verbrauch Heizung',
  Verbrauch_Warmwasser: 'Verbrauch Warmwasser',
  Energietraeger_Verbrauch: 'Energieträger (Verbrauch)',
  Verbrauchserfassung: 'Verbrauchserfassung',
  Leerstand: 'Leerstand',
  Warmwasseranteil: 'Warmwasseranteil',
  Rechnungen: 'Verbrauchsrechnungen',

  // Energieträger 1 (ETr1)
  ETr1_Kategorie: 'Energieträgerkategorie 1',
  ETr1_Heizung: 'Für Heizung verwendet',
  ETr1_TWW: 'Für Trinkwasser verwendet 1',
  ETr1_ZusatzHz: 'Zusatzheizung',
  ETr1_Lueften: 'Für Lüftung verwendet',
  ETr1_Licht: 'Für Licht verwendet',
  ETr1_Kuehlen: 'Für Kühlung verwendet',
  ETr1_Sonst: 'Für sonstige Verwendung',
  ETr1_PrimFaktor: 'Primärenergiefaktor',
  ETr1_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr1_Anteil_KWK: 'KWK-Anteil',
  ETr1_isFw: 'Energieträger ist Fernwärme',
  ETr1_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr1_Name: 'Name des Energieträgers 1',

  // Verbrauchszeiträume für ETr1
  ETr1_Jahr1_von: 'Beginn Messperiode 1',
  ETr1_Jahr1_bis: 'Ende Messperiode 1',
  ETr1_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr1_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr1_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr1_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr1_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr1_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr1_Jahr2_von: 'Beginn Messperiode 2',
  ETr1_Jahr2_bis: 'Ende Messperiode 2',
  ETr1_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr1_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr1_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr1_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr1_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr1_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr1_Jahr3_von: 'Beginn Messperiode 3',
  ETr1_Jahr3_bis: 'Ende Messperiode 3',
  ETr1_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr1_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr1_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr1_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr1_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr1_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Energieträger 2 (ETr2)
  ETr2_Kategorie: 'Energieträgerkategorie 2',
  ETr2_Heizung: 'Für Heizung verwendet',
  ETr2_TWW: 'Für Trinkwasser verwendet 2',
  ETr2_ZusatzHz: 'Zusatzheizung',
  ETr2_Lueften: 'Für Lüftung verwendet',
  ETr2_Licht: 'Für Licht verwendet',
  ETr2_Kuehlen: 'Für Kühlung verwendet',
  ETr2_Sonst: 'Für sonstige Verwendung',
  ETr2_PrimFaktor: 'Primärenergiefaktor',
  ETr2_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr2_Anteil_KWK: 'KWK-Anteil',
  ETr2_isFw: 'Energieträger ist Fernwärme',
  ETr2_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr2_Name: 'Name des Energieträgers 2',

  // Verbrauchszeiträume für ETr2
  ETr2_Jahr1_von: 'Beginn Messperiode 1',
  ETr2_Jahr1_bis: 'Ende Messperiode 1',
  ETr2_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr2_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr2_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr2_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr2_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr2_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr2_Jahr2_von: 'Beginn Messperiode 2',
  ETr2_Jahr2_bis: 'Ende Messperiode 2',
  ETr2_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr2_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr2_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr2_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr2_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr2_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr2_Jahr3_von: 'Beginn Messperiode 3',
  ETr2_Jahr3_bis: 'Ende Messperiode 3',
  ETr2_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr2_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr2_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr2_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr2_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr2_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Energieträger 3 (ETr3)
  ETr3_Kategorie: 'Energieträgerkategorie 3',
  ETr3_Heizung: 'Für Heizung verwendet',
  ETr3_TWW: 'Für Trinkwasser verwendet 3',
  ETr3_ZusatzHz: 'Zusatzheizung',
  ETr3_Lueften: 'Für Lüftung verwendet',
  ETr3_Licht: 'Für Licht verwendet',
  ETr3_Kuehlen: 'Für Kühlung verwendet',
  ETr3_Sonst: 'Für sonstige Verwendung',
  ETr3_PrimFaktor: 'Primärenergiefaktor',
  ETr3_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr3_Anteil_KWK: 'KWK-Anteil',
  ETr3_isFw: 'Energieträger ist Fernwärme',
  ETr3_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr3_Name: 'Name des Energieträgers 3',

  // Verbrauchszeiträume für ETr3
  ETr3_Jahr1_von: 'Beginn Messperiode 1',
  ETr3_Jahr1_bis: 'Ende Messperiode 1',
  ETr3_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr3_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr3_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr3_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr3_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr3_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr3_Jahr2_von: 'Beginn Messperiode 2',
  ETr3_Jahr2_bis: 'Ende Messperiode 2',
  ETr3_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr3_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr3_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr3_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr3_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr3_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr3_Jahr3_von: 'Beginn Messperiode 3',
  ETr3_Jahr3_bis: 'Ende Messperiode 3',
  ETr3_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr3_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr3_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr3_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr3_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr3_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Verbrauchsrechnungen
  verbrauchsrechnung1: 'Verbrauchsrechnung 1',
  verbrauchsrechnung2: 'Verbrauchsrechnung 2',
  verbrauchsrechnung3: 'Verbrauchsrechnung 3',

  // Grundrisse
  grundriss: 'Grundrisse'
};

// Add order_number to fieldLabels
fieldLabels.order_number = 'Bestellnummer';

// Component for displaying a section of data
interface DataSectionProps {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
  filesByField?: Record<string, any[]>; // For directory-based file handling
}

// Type for section configuration
interface SectionConfig {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
}

const DataSection: FC<DataSectionProps> = memo(({ 
  title, data, fields, excludeFields, filesByField 
}) => {
  // For Verbrauchsdaten section, add verbrauchsrechnung fields from filesByField if they exist
  let enhancedData = data;
  if (title === 'Verbrauchsdaten' && filesByField) {
    enhancedData = {
      ...data,
      // Add verbrauchsrechnung fields if files exist but database fields don't
      ...(filesByField.verbrauchsrechnung1?.length > 0 ? { verbrauchsrechnung1: 'Dateien verfügbar' } : {}),
      ...(filesByField.verbrauchsrechnung2?.length > 0 ? { verbrauchsrechnung2: 'Dateien verfügbar' } : {}),
      ...(filesByField.verbrauchsrechnung3?.length > 0 ? { verbrauchsrechnung3: 'Dateien verfügbar' } : {})
    };
  }

  // For Objektdaten section, add gebaeudebild and grundriss fields from filesByField if they exist
  if (title === 'Objektdaten' && filesByField) {
    enhancedData = {
      ...data,
      // Add gebaeudebild field if files exist but database field doesn't
      ...(filesByField.gebaeudebild?.length > 0 ? { gebaeudebild: 'Dateien verfügbar' } : {}),
      // Add grundriss field if files exist but database field doesn't
      ...(filesByField.grundriss?.length > 0 ? { grundriss: 'Dateien verfügbar' } : {})
    };
  }

  if (!enhancedData || Object.keys(enhancedData).length === 0) {
    return (
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-2">{title}</h2>
        <p className="text-gray-500 italic">Keine Daten vorhanden</p>
      </div>
    );
  }

  // Filter the data based on fields and excludeFields
  let filteredEntries = Object.entries(enhancedData || {});

  // If fields are specified, only include those fields
  if (fields) {
    if (Array.isArray(fields) && fields.length > 0) {
      filteredEntries = filteredEntries.filter(([key]) => fields.includes(key));
    } else if (typeof fields === 'object') {
      filteredEntries = filteredEntries.filter(([key]) => Object.keys(fields).includes(key));
    }
  }

  // If excludeFields are specified, exclude those fields
  if (excludeFields && excludeFields.length > 0) {
    filteredEntries = filteredEntries.filter(([key]) => !excludeFields.includes(key));
  }

  const displayData = Object.fromEntries(filteredEntries);

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-800 mb-2">{title}</h2>
      <div className="overflow-x-auto">
        {/* Updated table with fixed column widths using CSS Grid approach */}
        <table className="w-full bg-white border border-gray-200 table-fixed">
          <colgroup>
            {/* Fixed column widths: 40% for field labels, 60% for values */}
            <col className="w-2/5" />
            <col className="w-3/5" />
          </colgroup>
          <thead>
            <tr className="bg-gray-100">
              <th className="py-2 px-4 border-b border-r text-left font-medium text-gray-700 w-2/5">
                Feld
              </th>
              <th className="py-2 px-4 border-b text-left font-medium text-gray-700 w-3/5">
                Wert
              </th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(displayData).map(([key, value]) => (
              <tr key={key} className="hover:bg-gray-50">
                <td className="py-2 px-4 border-b border-r text-gray-700 w-2/5 break-words">
                  {/* Use the user-friendly label if available, otherwise use the technical field name */}
                  {typeof fields === 'object' && !Array.isArray(fields) && fields[key] ?
                    fields[key] : fieldLabels[key] || key}
                </td>
                <td className="py-2 px-4 border-b text-gray-700 w-3/5 break-words">
                  {/* Special handling for verbrauchsrechnung fields using directory-based files */}
                  {key && ['verbrauchsrechnung1', 'verbrauchsrechnung2', 'verbrauchsrechnung3'].includes(key) && filesByField ? (
                    <VerbrauchsrechnungFiles
                      files={filesByField[key] || []}
                      rechnungNumber={key.replace('verbrauchsrechnung', '')}
                    />
                  ) : key === 'gebaeudebild' && filesByField && filesByField.gebaeudebild?.length > 0 ? (
                    <GebaeudebildFiles files={filesByField.gebaeudebild} />
                  ) : key === 'grundriss' && filesByField && filesByField.grundriss?.length > 0 ? (
                    <GrundrissFiles files={filesByField.grundriss} />
                  ) : (
                    renderValue(value, key)
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
});

// Enum value mappings for better display
const enumValueLabels: Record<string, Record<string, string>> = {
  // Grundlegende Enums
  BedarfVerbrauch: {
    'V': 'Verbrauch',
    'B': 'Bedarf'
  },
  Anlass: {
    'AG_VERMIETUNG': 'Vermietung, Verkauf, Leasing',
    'AG_AUSHANG': 'Aushang',
    'AG_SONST': 'Sonstiges'
  },
  Datenerhebung: {
    '0': 'Eigentümer',
    '1': 'Aussteller',
    '2': 'Beide'
  },
  nichtWohnGeb: {
    '0': 'Wohngebäude',
    '1': 'Nichtwohngebäude'
  },
  isGebaeudehuelle: {
    '0': 'Mehrere Gebäude',
    '1': 'Ein Gebäude'
  },
  anbauSituation: {
    '0': 'Freistehend',
    '1': 'Einseitig angebaut',
    '2': 'Beidseitig angebaut'
  },
  Keller_beheizt: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Klimatisiert: {
    '0': 'Nein',
    '1': 'Ja'
  },
  WSchVo77_erfuellt: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Gebäudedetails 2
  Originaldaemmstandard: {
    '0': 'Normal',
    '1': 'Niedrigenergiehaus',
    '2': 'Passivhaus'
  },
  Fensterlüftung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Schachtlüftung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  L_Mit_WRG: {
    '0': 'Nein',
    '1': 'Ja'
  },
  L_Ohne_WRG: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Verbrauchsdaten - Boolean Felder
  ETr1_Heizung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_TWW: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_ZusatzHz: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Lueften: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Licht: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Kuehlen: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Sonst: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_isFw: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_gebaeudeNahErzeugt: {
    '0': 'Nein',
    '1': 'Ja'
  },

  ETr2_Heizung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_TWW: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_ZusatzHz: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Lueften: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Licht: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Kuehlen: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Sonst: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_isFw: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_gebaeudeNahErzeugt: {
    '0': 'Nein',
    '1': 'Ja'
  },

  ETr3_Heizung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_TWW: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_ZusatzHz: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Lueften: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Licht: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Kuehlen: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Sonst: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_isFw: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_gebaeudeNahErzeugt: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Leerstand hasLeerstand fields for all energy carriers and periods
  ETr1_Jahr1_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Jahr2_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr1_Jahr3_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Jahr1_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Jahr2_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr2_Jahr3_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Jahr1_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Jahr2_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },
  ETr3_Jahr3_Leerstand_hasLeerstand: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Energieträgerkategorien
  ETr1_Kategorie: {
    'BK_GAS': 'Erdgas',
    'BK_OEL': 'Heizöl',
    'BK_STROM': 'Strom',
    'BK_PELLET': 'Holzpellets',
    'BK_HACKSCHNITZEL': 'Holzhackschnitzel',
    'BK_STUECKHOLZ': 'Stückholz',
    'BK_KOHLE': 'Kohle',
    'BK_BIOGAS': 'Biogas',
    'BK_BIOOEL': 'Bioöl',
    'BK_FW70': 'Fernwärme (70%)',
    'BK_FW50': 'Fernwärme (50%)',
    'BK_FW0': 'Fernwärme (0%)',
    'BK_UMWELT': 'Umweltwärme',
    'BK_NAHW': 'Nahwärme',
    'BK_NACHTSTR': 'Strom(NT)',
    'BK_HOLZ': 'Holz',
    'BK_HHS': 'Holzhackschnitzel',
    'BK_FLUESSIGGAS': 'Flüssiggas',
    'BK_BRAUNKOHLE': 'Braunkohle'
  },
  ETr2_Kategorie: {
    'BK_GAS': 'Erdgas',
    'BK_OEL': 'Heizöl',
    'BK_STROM': 'Strom',
    'BK_PELLET': 'Holzpellets',
    'BK_HACKSCHNITZEL': 'Holzhackschnitzel',
    'BK_STUECKHOLZ': 'Stückholz',
    'BK_KOHLE': 'Kohle',
    'BK_BIOGAS': 'Biogas',
    'BK_BIOOEL': 'Bioöl',
    'BK_FW70': 'Fernwärme (70%)',
    'BK_FW50': 'Fernwärme (50%)',
    'BK_FW0': 'Fernwärme (0%)',
    'BK_UMWELT': 'Umweltwärme',
    'BK_NAHW': 'Nahwärme',
    'BK_NACHTSTR': 'Strom(NT)',
    'BK_HOLZ': 'Holz',
    'BK_HHS': 'Holzhackschnitzel',
    'BK_FLUESSIGGAS': 'Flüssiggas',
    'BK_BRAUNKOHLE': 'Braunkohle'
  },
  ETr3_Kategorie: {
    'BK_GAS': 'Erdgas',
    'BK_OEL': 'Heizöl',
    'BK_STROM': 'Strom',
    'BK_PELLET': 'Holzpellets',
    'BK_HACKSCHNITZEL': 'Holzhackschnitzel',
    'BK_STUECKHOLZ': 'Stückholz',
    'BK_KOHLE': 'Kohle',
    'BK_BIOGAS': 'Biogas',
    'BK_BIOOEL': 'Bioöl',
    'BK_FW70': 'Fernwärme (70%)',
    'BK_FW50': 'Fernwärme (50%)',
    'BK_FW0': 'Fernwärme (0%)',
    'BK_UMWELT': 'Umweltwärme',
    'BK_NAHW': 'Nahwärme',
    'BK_NACHTSTR': 'Strom(NT)',
    'BK_HOLZ': 'Holz',
    'BK_HHS': 'Holzhackschnitzel',
    'BK_FLUESSIGGAS': 'Flüssiggas',
    'BK_BRAUNKOHLE': 'Braunkohle'
  },

  // Fenster
  Fensterart: {
    'fb_HolzEinfach': 'Holzfenster, einfach verglast',
    'fb_HolzVerbund': 'Holzfenster, zweifach verglast (Kasten/Verbund)',
    'fb_HolzWSG': 'Holzfenster, Wärmeschutzglas',
    'fb_KunststoffWSG': 'Kunststoff mit Wärmeschutzverglasung',
    'fb_Kunststoff': 'Kunststofffenster, zweifach verglast',
    'fb_AluBis1983': 'Alu-/Stahlfenster, zweifach verglast - bis Bj. 83',
    'fb_AluAb1984': 'Alu-/Stahlfenster, zweifach verglast - ab Bj. 84',
    'fb_AluWSG': 'Alu-/Stahlfenster, Wärmeschutzglas',
    'fb_Tueren': 'Türen',
    'fb_Passivhaus': 'Passivhausfenster u. Türen (3-fach)',
    'fb_TuerenMetall': 'Metalltüren'
  },

  // Dynamische Fenster - Art
  Fenster1_art: {
    'fb_HolzEinfach': 'Holzfenster, einfach verglast',
    'fb_HolzVerbund': 'Holzfenster, zweifach verglast (Kasten/Verbund)',
    'fb_HolzWSG': 'Holzfenster, Wärmeschutzglas',
    'fb_KunststoffWSG': 'Kunststoff mit Wärmeschutzverglasung',
    'fb_Kunststoff': 'Kunststofffenster, zweifach verglast',
    'fb_AluBis1983': 'Alu-/Stahlfenster, zweifach verglast - bis Bj. 83',
    'fb_AluAb1984': 'Alu-/Stahlfenster, zweifach verglast - ab Bj. 84',
    'fb_AluWSG': 'Alu-/Stahlfenster, Wärmeschutzglas',
    'fb_Tueren': 'Türen',
    'fb_Passivhaus': 'Passivhausfenster u. Türen (3-fach)',
    'fb_TuerenMetall': 'Metalltüren'
  },
  Fenster2_art: {
    'fb_HolzEinfach': 'Holzfenster, einfach verglast',
    'fb_HolzVerbund': 'Holzfenster, zweifach verglast (Kasten/Verbund)',
    'fb_HolzWSG': 'Holzfenster, Wärmeschutzglas',
    'fb_KunststoffWSG': 'Kunststoff mit Wärmeschutzverglasung',
    'fb_Kunststoff': 'Kunststofffenster, zweifach verglast',
    'fb_AluBis1983': 'Alu-/Stahlfenster, zweifach verglast - bis Bj. 83',
    'fb_AluAb1984': 'Alu-/Stahlfenster, zweifach verglast - ab Bj. 84',
    'fb_AluWSG': 'Alu-/Stahlfenster, Wärmeschutzglas',
    'fb_Tueren': 'Türen',
    'fb_Passivhaus': 'Passivhausfenster u. Türen (3-fach)',
    'fb_TuerenMetall': 'Metalltüren'
  },
  Fenster3_art: {
    'fb_HolzEinfach': 'Holzfenster, einfach verglast',
    'fb_HolzVerbund': 'Holzfenster, zweifach verglast (Kasten/Verbund)',
    'fb_HolzWSG': 'Holzfenster, Wärmeschutzglas',
    'fb_KunststoffWSG': 'Kunststoff mit Wärmeschutzverglasung',
    'fb_Kunststoff': 'Kunststofffenster, zweifach verglast',
    'fb_AluBis1983': 'Alu-/Stahlfenster, zweifach verglast - bis Bj. 83',
    'fb_AluAb1984': 'Alu-/Stahlfenster, zweifach verglast - ab Bj. 84',
    'fb_AluWSG': 'Alu-/Stahlfenster, Wärmeschutzglas',
    'fb_Tueren': 'Türen',
    'fb_Passivhaus': 'Passivhausfenster u. Türen (3-fach)',
    'fb_TuerenMetall': 'Metalltüren'
  },

  // Dynamische Fenster - Ausrichtung
  Fenster1_ausrichtung: {
    '0': 'Nord (0°)',
    '45': 'Nordost (45°)',
    '90': 'Ost (90°)',
    '135': 'Südost (135°)',
    '180': 'Süd (180°)',
    '225': 'Südwest (225°)',
    '270': 'West (270°)',
    '315': 'Nordwest (315°)'
  },
  Fenster2_ausrichtung: {
    '0': 'Nord (0°)',
    '45': 'Nordost (45°)',
    '90': 'Ost (90°)',
    '135': 'Südost (135°)',
    '180': 'Süd (180°)',
    '225': 'Südwest (225°)',
    '270': 'West (270°)',
    '315': 'Nordwest (315°)'
  },
  Fenster3_ausrichtung: {
    '0': 'Nord (0°)',
    '45': 'Nordost (45°)',
    '90': 'Ost (90°)',
    '135': 'Südost (135°)',
    '180': 'Süd (180°)',
    '225': 'Südwest (225°)',
    '270': 'West (270°)',
    '315': 'Nordwest (315°)'
  },

  // Heizung
  Hzg_Übergabe: {
    '0': 'Heizkörper',
    '1': 'Flächenheizung'
  },
  Hzg_Verteilung_Art: {
    '0': 'Dezentral',
    '1': 'Gebäudezentral',
    '2': 'Wohnungszentral'
  },
  Hzg_kreistemperatur: {
    'HKTEMP_90_70': '90/70°C',
    'HKTEMP_70_55': '70/55°C',
    'HKTEMP_55_45': '55/45°C',
    'HKTEMP_35_28': '35/28°C'
  },
  Hzg_Verteilung_Dämmung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Hzg_Speicher: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Hzg_Aufstellung: {
    'HZ_ZENTRALHEIZUNG': 'Zentralheizung im Unbeheizten',
    'HZ_EINZELOFEN': 'Einzelgerät/Ofen',
    'HZ_ETAGENHEIZUNG': 'Zentralheizung (Zentralheizung/Etagenheizung im Beheizten)',
    'HZ_AUSSERHALB': 'Fernheizung (Außerhalb des Gebäudes)'
  },
  Hzg_Technik: {
    'HZT_STD_KESSEL': 'Standard-Kessel',
    'HZT_NT_KESSEL': 'Niedertemperatur-Kessel',
    'HZT_BW_KESSEL': 'Brennwert-Kessel',
    'HZT_KOLLEKTOR': 'Kollektor',
    'HZT_BW_GERAET': 'Brennwertgerät',
    'HZT_FERNHZ': 'Fernheizung',
    'HZT_FC': 'Brennstoffzelle',
    'HZT_NULL': 'keine Angaben',
    'HZT_WP_SORPTION': 'Sorptions-Gaswärmepumpe',
    'HZT_WP_MOTORISCH': 'Wärmepumpe (motorisch)',
    'HZT_WECHSELBR': 'Ofen oder Wechselbrand',
    'HZT_NACHTSP': 'Nachtspeicher-/Elektroheizung',
    'HZT_KWK': 'Kraft-Wärme-Kopplung',
    'HZT_HELLSTR': 'Hellstrahler',
    'HZT_DUNKELSTR': 'Dunkelstrahler',
    'HZT_LUFTHZ': 'Luftheizung'
  },
  Hzg_Energieträger: {
    'BK_GAS': 'Erdgas',
    'BK_OEL': 'Heizöl',
    'BK_STROM': 'Strom',
    'BK_PELLET': 'Holzpellets',
    'BK_STUECKHOLZ': 'Stückholz',
    'BK_HACKSCHNITZEL': 'Hackschnitzel',
    'BK_KOHLE': 'Kohle',
    'BK_FW70': 'Fernwärme (70% erneuerbar)',
    'BK_FW60': 'Fernwärme (60% erneuerbar)',
    'BK_FW40': 'Fernwärme (40% erneuerbar)',
    'BK_NACHTSTR': 'Strom(NT)',
    'BK_HOLZ': 'Holz',
    'BK_HHS': 'Holzhackschnitzel',
    'BK_FLUESSIGGAS': 'Flüssiggas',
    'BK_FW0': 'Nah-/Fernwärme aus Heizwerken',
    'BK_BRAUNKOHLE': 'Braunkohle',
    'BK_BIOOEL': 'Bioöl',
    'BK_BIOGAS': 'Biogas'
  },

  // Trinkwarmwasser & Lüftung
  TW_Verteilung_Art: {
    '0': 'Dezentral',
    '1': 'Gebäudezentral',
    '2': 'Wohnungszentral'
  },
  TW_Verteilung_Dämmung: {
    '0': 'Nein',
    '1': 'Ja'
  },
  TW_Zirkulation: {
    '0': 'Nein',
    '1': 'Ja'
  },
  TW_Speicher_Standort: {
    '1': 'Innerhalb',
    '2': 'Keller',
    '3': 'Dach'
  },
  TW_Technik: {
    'WT_HZG': 'Kombi-Erzeuger',
    'WT_EDL': 'Elektro-Durchlauferhitzer',
    'WT_SOLAR': 'Solarkollektor',
    'WT_WP': 'Wärmepumpe',
    'WT_FERNW': 'Fernwärme',
    'WT_ESP': 'Elektro-Speicher, elektrischer Heizstab',
    'WT_GASDIR': 'direkt brennstoffbeheizter Speicher (NT)',
    'WT_GASDIR2': 'direkt brennstoffbeheizter Speicher (BW)',
    'WT_GASDL': 'Gas-Durchlauferhitzer (NT)',
    'WT_GASDL2': 'Gas-Durchlauferhitzer (BW)',
    'WT_indHZG': 'indirekt beheizter Speicher (nur WW)'
  },
  TW_Solar: {
    '0': 'Nein',
    '1': 'Ja'
  },
  HZ_Solar: {
    '0': 'Nein',
    '1': 'Ja'
  },
  TW_WP: {
    '0': 'Nein',
    '1': 'Ja'
  },
  HZ_WP: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Luft_Lage: {
    'GAO_INNERHALB': 'Innerhalb der thermischen Hülle',
    'GAO_KELLER': 'Außerhalb der thermischen Hülle (Keller)',
    'GAO_DACHRAUM': 'Außerhalb der thermischen Hülle (Dach)'
  },
  Luft_Typ: {
    'LA_FREI': 'freie Lüftung',
    'LA_ABL': 'Abluftanlage',
    'LA_WP': 'Abluftwärmepumpe',
    'LA_WRG': 'Zu-/Abluft mit Wärmerückgewinnung'
  },


  // Bauteile - Materialien
  massiv: {
    'kb_Holz': 'Holz',
    'kb_Stahlbeton': 'Stahlbeton',
    'kb_zweischaligOhneDaemm': 'Zweischalig ohne Dämmung',
    'kb_zweischaligMitDaemm': 'Zweischalig mit Dämmung',
    'kb_Massiv': 'Ziegel/Hohlstein (massive Konstruktion)',
    'kb_MassivBis20': 'Massivwand bis 20 cm Vollziegel, Naturstein, KS',
    'kb_Massiv20bis30': 'Massivwand 20-30 cm Vollziegel, Naturstein, KS',
    'kb_Massivueber30': 'Massivwand über 30 cm Vollziegel, Naturstein, KS',
    'kb_sonstMassbis20': 'Sonst. Massivwand bis 20 cm',
    'kb_sonstMassuebber20': 'Sonst. Massivwand über 20 cm',
    'kb_Massivholz': 'Holz massiv (Blockbohlen)',
    'kb_FachwerkLehm': 'Fachwerk mit Lehmfachung',
    'kb_FachwerkVollziegel': 'Fachwerk mit Vollziegelfachung',
    'kb_Rollladen_gedaemmt': 'Rollladenkasten gedämmt',
    'kb_Rollladen_ungedaemmt': 'Rollladenkasten ungedämmt',
    '0': 'Holzbalken',
    '1': 'Massivdecke'
  },

  // Bauteile - Übergänge
  uebergang: {
    '0': 'Übergang zu Erdreich/Unbeheiztem Dachraum',
    '1': 'Übergang zu unbeheiztem Keller/Direkt bewittert'
  },

  // Böden - Material (spezifisch)
  Boden1_massiv: {
    'kb_Massiv': 'Ziegel/Hohlstein',
    'kb_Holz': 'Holz',
    'kb_Stahlbeton': 'Stahlbeton'
  },
  Boden2_massiv: {
    'kb_Massiv': 'Ziegel/Hohlstein',
    'kb_Holz': 'Holz',
    'kb_Stahlbeton': 'Stahlbeton'
  },

  // Böden - Kellerdecke
  Boden1_Kellerdecke: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Boden2_Kellerdecke: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Dächer - Material (spezifisch)
  Dach1_massiv: {
    '0': 'Holzbalken',
    '1': 'Massivdecke'
  },
  Dach2_massiv: {
    '0': 'Holzbalken',
    '1': 'Massivdecke'
  },

  // Dächer - Geschossdecke
  Dach1_Geschossdecke: {
    '0': 'Nein',
    '1': 'Ja'
  },
  Dach2_Geschossdecke: {
    '0': 'Nein',
    '1': 'Ja'
  },

  // Wände - Material (spezifisch)
  Wand1_massiv: {
    'kb_Holz': 'Holz',
    'kb_Stahlbeton': 'Stahlbeton',
    'kb_zweischaligOhneDaemm': 'Zweischalig ohne Dämmung',
    'kb_zweischaligMitDaemm': 'Zweischalig mit Dämmung',
    'kb_Massiv': 'Ziegel/Hohlstein (massive Konstruktion)',
    'kb_MassivBis20': 'Massivwand bis 20 cm Vollziegel, Naturstein, KS',
    'kb_Massiv20bis30': 'Massivwand 20-30 cm Vollziegel, Naturstein, KS',
    'kb_Massivueber30': 'Massivwand über 30 cm Vollziegel, Naturstein, KS',
    'kb_sonstMassbis20': 'Sonst. Massivwand bis 20 cm',
    'kb_sonstMassuebber20': 'Sonst. Massivwand über 20 cm',
    'kb_Massivholz': 'Holz massiv (Blockbohlen)',
    'kb_FachwerkLehm': 'Fachwerk mit Lehmfachung',
    'kb_FachwerkVollziegel': 'Fachwerk mit Vollziegelfachung',
    'kb_Rollladen_gedaemmt': 'Rollladenkasten gedämmt',
    'kb_Rollladen_ungedaemmt': 'Rollladenkasten ungedämmt'
  },
  Wand2_massiv: {
    'kb_Holz': 'Holz',
    'kb_Stahlbeton': 'Stahlbeton',
    'kb_zweischaligOhneDaemm': 'Zweischalig ohne Dämmung',
    'kb_zweischaligMitDaemm': 'Zweischalig mit Dämmung',
    'kb_Massiv': 'Ziegel/Hohlstein (massive Konstruktion)',
    'kb_MassivBis20': 'Massivwand bis 20 cm Vollziegel, Naturstein, KS',
    'kb_Massiv20bis30': 'Massivwand 20-30 cm Vollziegel, Naturstein, KS',
    'kb_Massivueber30': 'Massivwand über 30 cm Vollziegel, Naturstein, KS',
    'kb_sonstMassbis20': 'Sonst. Massivwand bis 20 cm',
    'kb_sonstMassuebber20': 'Sonst. Massivwand über 20 cm',
    'kb_Massivholz': 'Holz massiv (Blockbohlen)',
    'kb_FachwerkLehm': 'Fachwerk mit Lehmfachung',
    'kb_FachwerkVollziegel': 'Fachwerk mit Vollziegelfachung',
    'kb_Rollladen_gedaemmt': 'Rollladenkasten gedämmt',
    'kb_Rollladen_ungedaemmt': 'Rollladenkasten ungedämmt'
  }
};


/**
 * Component to render gebaeudebild files using directory-based approach
 */
const GebaeudebildFiles: FC<{
  files: any[];
}> = memo(({ files }) => {
  if (!files || files.length === 0) {
    return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
  }

  return (
    <div className="space-y-2">
      {files.map((file, index) => (
        <div key={index} className="flex items-center space-x-2">
          <FileWithSignedUrl
            path={file.path}
            label={file.originalName || 'Gebäudebild'}
          />
          <span className="text-xs text-gray-500">
            ({files.length > 1 ? `${index + 1} von ${files.length}` : 'Einzeldatei'})
          </span>
        </div>
      ))}
    </div>
  );
});

/**
 * Component to render verbrauchsrechnung files using directory-based approach
 */
const VerbrauchsrechnungFiles: FC<{
  files: any[];
  rechnungNumber: string;
}> = memo(({ files, rechnungNumber }) => {
  if (!files || files.length === 0) {
    return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
  }

  return (
    <div className="space-y-2">
      {files.map((file, index) => (
        <div key={index} className="flex items-center space-x-2">
          <FileWithSignedUrl
            path={file.path}
            label={file.originalName || `Verbrauchsrechnung ${rechnungNumber}`}
          />
          <span className="text-xs text-gray-500">
            ({files.length > 1 ? `${index + 1} von ${files.length}` : 'Einzeldatei'})
          </span>
        </div>
      ))}
    </div>
  );
});

/**
 * Component to render grundriss files using directory-based approach
 */
const GrundrissFiles: FC<{
  files: any[];
}> = memo(({ files }) => {
  if (!files || files.length === 0) {
    return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
  }

  return (
    <div className="space-y-2">
      {files.map((file, index) => (
        <div key={index} className="flex items-center space-x-2">
          <FileWithSignedUrl
            path={file.path}
            label={file.originalName || 'Grundriss'}
          />
          <span className="text-xs text-gray-500">
            ({files.length > 1 ? `${index + 1} von ${files.length}` : 'Einzeldatei'})
          </span>
        </div>
      ))}
    </div>
  );
});

const renderValue = (value: any, fieldName?: string): ReactNode => {
  if (value === null || value === undefined) {
    return <span className="text-gray-400">-</span>;
  }

  if (typeof value === 'boolean') {
    return value ? 'Ja' : 'Nein';
  }

  // File fields are now handled by the directory-based approach in DataSection component
  // No special handling needed here for file fields

  // Handle enum values if we know the field name and have a mapping for it
  if (fieldName && typeof value === 'string' && enumValueLabels[fieldName] && enumValueLabels[fieldName][value]) {
    return enumValueLabels[fieldName][value];
  }

  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return (
        <ul className="list-disc list-inside">
          {value.map((item, index) => (
            <li key={index} className="mb-1">
              {typeof item === 'object' ? JSON.stringify(item) : String(item)}
            </li>
          ))}
        </ul>
      );
    }

    // Check if it's a file/image URL (other than gebaeudebild and verbrauchsrechnung)
    if (value.publicUrl || (typeof value === 'string' && (value.startsWith('http') || value.includes('storage')))) {
      return (
        <a
          href={value.publicUrl || value}
          target="_blank"
          rel="noopener noreferrer"
          className="text-green-600 hover:underline"
        >
          Datei anzeigen
        </a>
      );
    }

    return JSON.stringify(value);
  }

  return String(value);
};

export const ZusammenfassungPage = () => {
  const navigate = useNavigate();
  
  const [error, setError] = useState<string | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const { activeCertificateId } = useCertificate();
  const { isAnonymous } = useAuth();

  // Reduce console logging - only log when essential state changes
  const renderCount = useMemo(() => Date.now(), [activeCertificateId, error, isProcessingPayment]);
  useEffect(() => {
    console.log(`🏠 ZusammenfassungPage state changed ${renderCount}:`, {
      hasError: !!error,
      isProcessingPayment,
      activeCertificateId,
      isAnonymous
    });
  }, [renderCount, error, isProcessingPayment, activeCertificateId, isAnonymous]);

  // Mark this page as visited for navigation tracking
  usePageVisit('zusammenfassung');

  // Memoize the certificate ID to prevent unnecessary re-renders
  const memoizedCertificateId = useMemo(() => activeCertificateId, [activeCertificateId]);


  // Use the directory-based file management hook
  const { filesByField } = useCertificateFiles(memoizedCertificateId);
  
  const queryClient = useQueryClient();

  // Account conversion modal state
  const [showAccountConversion, setShowAccountConversion] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);


  useEffect(() => {
    console.log('🏠 Modal state in parent:', { 
      showAccountConversion, 
      userEmail 
    });
  }, [showAccountConversion, userEmail]);


  // Legal consent state
  const [legalConsent, setLegalConsent] = useState({
    agb: false,
    datenschutz: false,
    widerruf: false,
    dataAccuracy: false
  });

  // Fetch all energieausweis data for the current user with optimized caching
  const { data: energieausweisData, isError, isLoading: queryLoading } = useQuery({
    queryKey: ['energieausweise', 'all', memoizedCertificateId],
    queryFn: async () => {
      console.log('🔍 Fetching energieausweis data for:', memoizedCertificateId);
      if (!memoizedCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', memoizedCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data as EnergieausweisData;
    },
    enabled: !!memoizedCertificateId,
    retry: false,
    staleTime: 2 * 60 * 1000, // 2 minutes - prevent unnecessary refetches
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus - key optimization
    refetchOnMount: false, // Only refetch if data is stale
    refetchOnReconnect: false, // Don't refetch on network reconnect
  });

  // Optimize logging to only trigger when essential state changes
  useEffect(() => {
    console.log('🔍 Query state changed:', {
      hasData: !!energieausweisData,
      isError,
      queryLoading
    });
  }, [!!energieausweisData, isError, queryLoading]); // Use boolean instead of object reference
  

  // More granular payment status checks for better retry handling
  const isPaymentInProgress = energieausweisData?.status === 'payment_initiated';
  const isPaymentFailed = energieausweisData?.status &&
    ['payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);
  const isPaid = energieausweisData?.status === 'payment_complete';

  // Legacy check for backward compatibility (used in other parts of the component)
  const isPaymentStatus = energieausweisData?.status &&
    ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);

  // Fetch pricing information for the current certificate type
  const { data: pricingInfo, isLoading: pricingLoading } = useQuery({
    queryKey: ['pricing', energieausweisData?.certificate_type],
    queryFn: async () => {
      if (!energieausweisData?.certificate_type) return null;
      return await PricingService.getPricingDisplayInfoForType(energieausweisData.certificate_type as any);
    },
    enabled: !!energieausweisData?.certificate_type,
    retry: false,
  });

  // Mutation to update certificate status to 'zusammenfassung'
  const updateStatusMutation = useMutation({
    mutationFn: async () => {
      if (!memoizedCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // First check current status to ensure unidirectional progression
      const { data: currentData, error: fetchError } = await supabase
        .from('energieausweise')
        .select('status')
        .eq('id', memoizedCertificateId)
        .single();

      if (fetchError) throw fetchError;

      // Only prevent regression from successful payment - allow retry from failed states
      if (currentData?.status === 'payment_complete') {
        console.log(`Status update prevented: Cannot regress from successful payment status '${currentData.status}' to 'zusammenfassung'`);
        // Return null to indicate no update was performed
        return null;
      }

      const { data, error } = await supabase
        .from('energieausweise')
        .update({
          status: 'zusammenfassung',
          updated_at: new Date().toISOString(),
        })
        .eq('id', memoizedCertificateId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data) {
        console.log('Certificate status updated to zusammenfassung:', data.id);
        // Update the query cache to reflect the new status
        queryClient.setQueryData(['energieausweise', 'all', memoizedCertificateId], data);
        queryClient.invalidateQueries({ queryKey: ['energieausweise'] });
      } else {
        console.log('Status update was prevented due to unidirectional progression rules');
      }
    },
    onError: (error) => {
      console.error('Error updating certificate status:', error);
      // Don't show error to user as this is a background operation
      // The certificate is still functional even if status update fails
    },
  });

  useEffect(() => {
    if (isError) {
      setError('Fehler beim Laden der Daten. Bitte versuchen Sie es später erneut.');
    }
  }, [queryLoading, isError]);

  // Memoize status values to prevent unnecessary effect triggers
  const certificateStatus = energieausweisData?.status;
  const shouldUpdateStatus = useMemo(() => {
    return certificateStatus &&
           certificateStatus !== 'zusammenfassung' &&
           certificateStatus !== 'payment_complete';
  }, [certificateStatus]);

  // Effect to automatically update certificate status to 'zusammenfassung' when reaching summary page
  useEffect(() => {
    // Only update status if conditions are met and we're not already processing
    if (
      shouldUpdateStatus &&
      !updateStatusMutation.isPending &&
      !updateStatusMutation.isSuccess
    ) {
      console.log('Updating certificate status to zusammenfassung for certificate:', memoizedCertificateId);
      updateStatusMutation.mutate();
    }
   }, [
    shouldUpdateStatus,
    memoizedCertificateId,
    updateStatusMutation.isPending,
    updateStatusMutation.isSuccess,
    updateStatusMutation.mutate
  ]);

  // Get user email from certificate data for anonymous users
  useEffect(() => {
    let isMounted = true;
    
    const fetchUserEmail = async () => {
      if (isAnonymous && memoizedCertificateId && !userEmail) {
        const email = await getUserEmailFromCertificate(memoizedCertificateId);
        if (isMounted) {
          setUserEmail(email);
        }
      }
    };

    fetchUserEmail();
    
    return () => {
      isMounted = false;
    };
  }, [isAnonymous, memoizedCertificateId]);


  // Memoize session timeout conditions to prevent unnecessary effect triggers
  const hasDataForTimeout = !!energieausweisData;

  // Session timeout warning - differentiated for anonymous vs authenticated users
  useEffect(() => {
    if (!hasDataForTimeout || isProcessingPayment) return;

    let timeoutWarning: NodeJS.Timeout;
    let sessionExpiry: NodeJS.Timeout;

    if (isAnonymous) {
      timeoutWarning = setTimeout(() => {
        setError('Ihre Sitzung läuft in 5 Minuten ab. Erstellen Sie jetzt ein Konto, um Ihre Daten dauerhaft zu sichern und den Datenverlust zu vermeiden.');
      }, 25 * 60 * 1000);

      sessionExpiry = setTimeout(() => {
        setError('Ihre Sitzung ist abgelaufen. Ihre eingegebenen Daten könnten verloren gegangen sein. Bitte laden Sie die Seite neu und versuchen Sie es erneut.');
      }, 30 * 60 * 1000);
    } else {
      timeoutWarning = setTimeout(() => {
        setError('Ihre Sitzung läuft in 10 Minuten ab. Ihre Daten sind sicher in Ihrem Konto gespeichert.');
      }, 50 * 60 * 1000);

      sessionExpiry = setTimeout(() => {
        setError('Ihre Sitzung ist abgelaufen. Laden Sie die Seite neu - alle Ihre Daten sind sicher in Ihrem Konto gespeichert.');
      }, 60 * 60 * 1000);
    }

    return () => {
      clearTimeout(timeoutWarning);
      clearTimeout(sessionExpiry);
    };
  }, [hasDataForTimeout, isProcessingPayment, isAnonymous]); // Use memoized boolean


  // Mutation to update certificate status to 'payment_initiated' when checkout starts
  const updatePaymentInitiatedMutation = useMutation({
    mutationFn: async () => {
      if (!activeCertificateId) throw new Error('Kein aktives Zertifikat ausgewählt.');

      // First check current status to ensure unidirectional progression
      const { data: currentData, error: fetchError } = await supabase
        .from('energieausweise')
        .select('status')
        .eq('id', activeCertificateId)
        .single();

      if (fetchError) throw fetchError;

      // Only prevent retry from successful payment - allow retry from failed states
      if (currentData?.status === 'payment_complete') {
        console.log(`Status update prevented: Cannot retry payment after successful completion '${currentData.status}'`);
        return null;
      }

      const { data, error } = await supabase
        .from('energieausweise')
        .update({
          status: 'payment_initiated',
          updated_at: new Date().toISOString(),
        })
        .eq('id', activeCertificateId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      if (data) {
        console.log('Certificate status updated to payment_initiated:', data.id);
        // Update the query cache to reflect the new status
        queryClient.setQueryData(['energieausweise', 'all', activeCertificateId], data);
        queryClient.invalidateQueries({ queryKey: ['energieausweise'] });

        // Now proceed with creating the checkout session
        createCheckoutMutation.mutate();
      } else {
        console.log('Payment initiation was prevented - certificate already successfully paid');
        setError('Zahlung kann nicht gestartet werden. Zertifikat wurde bereits erfolgreich bezahlt.');
        setIsProcessingPayment(false);
      }
    },
    onError: (error) => {
      console.error('Error updating certificate status to payment_initiated:', error);
      setError('Fehler beim Aktualisieren des Zertifikatsstatus.');
      setIsProcessingPayment(false);
    },
  });

  // Track payment attempt when checkout session is created
  const trackPaymentAttempt = async (sessionId: string) => {
    if (!activeCertificateId) return;

    try {
      // Create payment attempt record
      const { error } = await supabase
        .from('payment_attempts')
        .insert({
          certificate_id: activeCertificateId,
          stripe_session_id: sessionId,
          attempt_status: 'initiated',
          amount_cents: 4900,
          currency: 'eur',
          user_agent: navigator.userAgent,
          created_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error tracking payment attempt:', error);
      } else {
        console.log('Payment attempt tracked successfully');
      }
    } catch (error) {
      console.error('Error tracking payment attempt:', error);
    }
  };

  // Mutation for creating Stripe checkout session
  const createCheckoutMutation = useMutation({
    mutationFn: async () => {
      if (!activeCertificateId || !energieausweisData) {
        throw new Error('Keine Daten verfügbar für die Zahlung.');
      }

      // Get current user for email
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Benutzer nicht eingeloggt.');
      }

      // Generate URLs using the router pattern but still need to use full URLs for Stripe
      const successUrl = `${window.location.origin}/payment-success?certificate_id=${activeCertificateId}`;
      const cancelUrl = `${window.location.origin}/payment-cancel`;

      // Call the Supabase Edge Function to create checkout session
      const { data, error } = await supabase.functions.invoke('create-checkout-session', {
        body: {
          certificateType: energieausweisData.certificate_type,
          successUrl: successUrl,
          cancelUrl: cancelUrl,
          paymentId: activeCertificateId,
          userEmail: user.email,
          orderNumber: `EA-${activeCertificateId.slice(-8).toUpperCase()}`
        }
      });

      if (error) {
        throw new Error(error.message || 'Fehler beim Erstellen der Checkout-Session.');
      }

      if (!data?.url) {
        throw new Error('Keine Checkout-URL erhalten.');
      }

      return data;
    },
    onSuccess: async (data) => {
      // Update any relevant queries that might be affected by this operation
      queryClient.invalidateQueries({ queryKey: ['energieausweise', activeCertificateId] });

      // Store the checkout session ID in localStorage for potential recovery
      if (data.id) {
        localStorage.setItem('lastCheckoutSessionId', data.id);

        // Track payment attempt
        await trackPaymentAttempt(data.id);
      }

      // For Stripe Checkout, we still need to use window.location.href
      // as it's an external redirect to Stripe's hosted page
      window.location.href = data.url;
    },
    onError: (error: CheckoutError) => {
      console.error('Checkout error:', error);

      // Handle specific error codes if available
      if (error.code === 'payment_failed') {
        setError('Die Zahlung konnte nicht verarbeitet werden. Bitte versuchen Sie es erneut.');
      } else if (error.code === 'session_creation_failed') {
        setError('Fehler beim Erstellen der Zahlungssitzung. Bitte versuchen Sie es später erneut.');
      } else {
        setError(error.message || 'Fehler beim Starten der Zahlung.');
      }

      setIsProcessingPayment(false);
    }
  });

  // Function to save form state before checkout
  const saveFormState = () => {
    if (energieausweisData) {
      // Save the current state to localStorage
      localStorage.setItem('checkoutFormState', JSON.stringify({
        certificateId: activeCertificateId,
        timestamp: new Date().toISOString()
      }));
    }
  };

  // Function to handle checkout button click
  const handleCheckout = async () => {
    if (!energieausweisData) {
      setError('Keine Daten verfügbar. Bitte füllen Sie zuerst alle Formulare aus.');
      return;
    }

    // Check legal consent
    if (!legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy) {
      setError('Bitte stimmen Sie allen rechtlichen Bestimmungen zu, um fortzufahren.');
      return;
    }

    // For anonymous users, show account conversion modal first
    if (isAnonymous) {
      setShowAccountConversion(true);
      return;
    }

    // Save current form state before proceeding
    saveFormState();

    setIsProcessingPayment(true);
    setError(null);

    // First update status to 'payment_initiated', then create checkout session
    updatePaymentInitiatedMutation.mutate();
  };

  // Function to handle account conversion success
  const handleAccountConversionSuccess = () => {
    setShowAccountConversion(false);
    // Proceed with checkout after successful account conversion
    saveFormState();
    setIsProcessingPayment(true);
    setError(null);

    // First update status to 'payment_initiated', then create checkout session
    updatePaymentInitiatedMutation.mutate();
  };

  // Function to handle account conversion modal close (user cancelled)
  const handleAccountConversionClose = () => {
    setShowAccountConversion(false);
    // Reset processing state since user cancelled
    setIsProcessingPayment(false);
    setError(null);
  };

  // Function to handle back button navigation based on certificate type
  const handleBackNavigation = () => {
    if (!energieausweisData?.certificate_type) {
      // Fallback to objektdaten page if certificate type is not available
      navigate({ to: '/erfassen/objektdaten' });
      return;
    }

    // Navigate based on certificate type following the established routing flow
    if (energieausweisData.certificate_type === 'WG/B') {
      // For WG/B: Navigate back to TwwLueftungPage (since VerbrauchPage is skipped)
      navigate({ to: '/erfassen/tww-lueftung' });
    } else {
      // For WG/V and NWG/V: Navigate back to VerbrauchPage (since TwwLueftungPage is skipped)
      navigate({ to: '/erfassen/verbrauch' });
    }
  };

  // Customer fields to be excluded from Objektdaten
  const kundenFields = [
    'Kunden_Anrede', 'Kunden_Vorname', 'Kunden_Nachname', 'Kunden_Strasse',
    'Kunden_Hausnr', 'Kunden_PLZ', 'Kunden_Ort', 'Kunden_email', 'Kunden_telefon'
  ];

  // Get certificate type label
  const getCertificateTypeLabel = (type: string | null | undefined) => {
    switch (type) {
      case 'WG/V':
        return 'Wohngebäude-Verbrauchsausweis';
      case 'WG/B':
        return 'Wohngebäude-Bedarfsausweis';
      case 'NWG/V':
        return 'Nicht-Wohngebäude-Verbrauchsausweis';
      default:
        return 'Nicht angegeben';
    }
  };

  // Memoize individual data sections to prevent unnecessary recalculations
  const orderInfo = useMemo(() => ({
    order_number: energieausweisData?.order_number || `EA-${energieausweisData?.id?.slice(-8).toUpperCase()}`
  }), [energieausweisData?.order_number, energieausweisData?.id]);

  const certificateTypeInfo = useMemo(() => ({
    certificate_type: getCertificateTypeLabel(energieausweisData?.certificate_type)
  }), [energieausweisData?.certificate_type]);

  // Memoize processed building details to avoid expensive array operations on every render
  const processedGebaeudedetails2 = useMemo(() => {
    if (!energieausweisData?.gebaeudedetails2) return null;

    const details = { ...energieausweisData.gebaeudedetails2 };

    // Process boeden array if it exists
    if (details.boeden && Array.isArray(details.boeden)) {
      details.boeden.forEach((boden: any, index: number) => {
        const prefix = `Boden${index + 1}`;
        details[prefix] = boden.bezeichnung;
        details[`${prefix}_massiv`] = boden.massiv;
        details[`${prefix}_Kellerdecke`] = boden.uebergang;
        details[`${prefix}_Fläche`] = boden.flaeche;
        details[`${prefix}_Dämmung`] = boden.daemmung;
      });
    }

    // Process daecher array if it exists
    if (details.daecher && Array.isArray(details.daecher)) {
      details.daecher.forEach((dach: any, index: number) => {
        const prefix = `Dach${index + 1}`;
        details[prefix] = dach.bezeichnung;
        details[`${prefix}_massiv`] = dach.massiv;
        details[`${prefix}_Geschossdecke`] = dach.uebergang;
        details[`${prefix}_Fläche`] = dach.flaeche;
        details[`${prefix}_Dämmung`] = dach.daemmung;
      });
    }

    // Process waende array if it exists
    if (details.waende && Array.isArray(details.waende)) {
      details.waende.forEach((wand: any, index: number) => {
        const prefix = `Wand${index + 1}`;
        details[prefix] = wand.bezeichnung;
        details[`${prefix}_massiv`] = wand.massiv;
        details[`${prefix}_Fläche`] = wand.flaeche;
        details[`${prefix}_Dämmung`] = wand.daemmung;
      });
    }

    return details;
  }, [energieausweisData?.gebaeudedetails2]);

  // Memoize processed fenster data
  const processedFensterData = useMemo(() => {
    if (!energieausweisData?.fenster?.fenster) return null;

    const result: Record<string, any> = {};
    energieausweisData.fenster.fenster.forEach((fenster: any, index: number) => {
      const prefix = `Fenster${index + 1}_`;
      result[`${prefix}bezeichnung`] = fenster.bezeichnung;
      result[`${prefix}art`] = fenster.art;
      result[`${prefix}flaeche`] = fenster.flaeche;
      result[`${prefix}ausrichtung`] = fenster.ausrichtung;
      result[`${prefix}baujahr`] = fenster.baujahr;
    });
    return result;
  }, [energieausweisData?.fenster?.fenster]);

  // Memoize combined TWW & Lüftung data
  const combinedTwwLueftungData = useMemo(() => ({
    ...(energieausweisData?.trinkwarmwasser || {}),
    ...(energieausweisData?.lueftung || {})
  }), [energieausweisData?.trinkwarmwasser, energieausweisData?.lueftung]);

  // Organize data into sections with optimized dependencies
  const sections: SectionConfig[] = useMemo(() => {
    if (!energieausweisData) return [];

    const baseSections = [
      {
        title: 'Bestellinformationen',
        data: orderInfo,
        fields: { order_number: 'Bestellnummer' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Ausweistyp',
        data: certificateTypeInfo,
        fields: { certificate_type: 'Energieausweistyp' } as Record<string, string>,
        excludeFields: []
      },
      {
        title: 'Objektdaten',
        data: energieausweisData.objektdaten,
        excludeFields: kundenFields
      },
      {
        title: 'Kundendaten',
        data: energieausweisData.objektdaten,
        fields: kundenFields
      },
      {
        title: 'Gebäudedetails (Teil 1)',
        data: energieausweisData.gebaeudedetails1
      },
      {
        title: 'Gebäudedetails (Teil 2)',
        data: processedGebaeudedetails2,
        excludeFields: ['boeden', 'daecher', 'waende']
      }
    ];

    // Add conditional sections based on certificate type
    if (energieausweisData.certificate_type === 'WG/B') {
      baseSections.push(
        {
          title: 'Fenster',
          data: processedFensterData
        },
        {
          title: 'Heizung',
          data: energieausweisData.heizung
        },
        {
          title: 'Trinkwarmwasser & Lüftung',
          data: combinedTwwLueftungData
        }
      );
    }

    if (energieausweisData.certificate_type === 'WG/V' || energieausweisData.certificate_type === 'NWG/V') {
      baseSections.push({
        title: 'Verbrauchsdaten',
        data: energieausweisData.verbrauchsdaten
      });
    }

    return baseSections;
  }, [
    energieausweisData?.certificate_type,
    orderInfo,
    certificateTypeInfo,
    energieausweisData?.objektdaten,
    energieausweisData?.gebaeudedetails1,
    processedGebaeudedetails2,
    processedFensterData,
    energieausweisData?.heizung,
    combinedTwwLueftungData,
    energieausweisData?.verbrauchsdaten
  ]);

  return (
    <div className="max-w-5xl mx-auto px-4 py-8">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          Zusammenfassung Ihres Energieausweises
        </h1>
      </div>

      {/* Anonymous User Indicator */}
      <AnonymousUserIndicator className="mb-6" showFullMessage={false} />

      {/* Status-specific description */}
      {isPaid ? (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 font-medium">
                Ihr Energieausweis wurde erfolgreich bezahlt und ist abgeschlossen.
              </p>
              <p className="text-sm text-green-600 mt-1">
                Hier sehen Sie eine Zusammenfassung aller Daten Ihres Energieausweises.
              </p>
            </div>
          </div>
        </div>
      ) : isPaymentInProgress ? (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700 font-medium">
                Zahlung läuft - Sie werden zur Bezahlung weitergeleitet.
              </p>
              <p className="text-sm text-blue-600 mt-1">
                Falls Sie nicht automatisch weitergeleitet wurden, laden Sie bitte die Seite neu.
              </p>
            </div>
          </div>
        </div>
      ) : isPaymentFailed ? (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700 font-medium">
                {energieausweisData?.status === 'payment_failed' && 'Zahlung fehlgeschlagen - Sie können es erneut versuchen.'}
                {energieausweisData?.status === 'payment_expired' && 'Zahlungssitzung abgelaufen - Bitte versuchen Sie es erneut.'}
                {energieausweisData?.status === 'payment_disputed' && 'Zahlung wurde bestritten - Bitte kontaktieren Sie den Support oder versuchen Sie eine neue Zahlung.'}
              </p>
              <p className="text-sm text-yellow-600 mt-1">
                Keine Sorge - es wurden keine Gebühren erhoben. Sie können die Zahlung jederzeit wiederholen.
              </p>
            </div>
          </div>
        </div>
      ) : (
        <p className="text-lg text-gray-600 mb-8">
          Hier sehen Sie eine Zusammenfassung aller eingegebenen Daten für Ihren Energieausweis.
          Bitte überprüfen Sie die Daten sorgfältig, bevor Sie fortfahren.
        </p>
      )}

      {error ? (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      ) : !energieausweisData ? (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Es wurden keine Daten gefunden. Bitte füllen Sie zuerst die Formulare aus.
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg p-6">
          <div className="grid grid-cols-1 gap-6">
            {sections.map((section, index) => (
              <DataSection
                key={index}
                title={section.title}
                data={section.data}
                fields={section.fields}
                excludeFields={section.excludeFields}
                filesByField={section.title === 'Verbrauchsdaten' || section.title === 'Objektdaten' ? filesByField : undefined}
              />
            ))}
          </div>

          {/* Dynamic Pricing Overview Table */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 mb-6">
                            

              {/* Enhanced Selection Summary */}
              <div className="mt-6 bg-white border-2 border-green-300 rounded-lg p-6 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-4 h-4 rounded-full mr-3 ${
                      energieausweisData?.certificate_type === 'WG/V' ? 'bg-green-500' :
                      energieausweisData?.certificate_type === 'WG/B' ? 'bg-blue-500' :
                      energieausweisData?.certificate_type === 'NWG/V' ? 'bg-purple-500' : 'bg-gray-400'
                    }`}></div>
                    <div>
                      <div className="text-lg font-semibold text-gray-900">
                        Ihre Auswahl: {getCertificateTypeLabel(energieausweisData?.certificate_type)}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {energieausweisData?.certificate_type === 'WG/V' && 'Verbrauchsbasierte Bewertung für Wohngebäude'}
                        {energieausweisData?.certificate_type === 'WG/B' && 'Bedarfsbasierte Bewertung mit detaillierter Gebäudeanalyse'}
                        {energieausweisData?.certificate_type === 'NWG/V' && 'Verbrauchsbasierte Bewertung für Nichtwohngebäude'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600">
                      {pricingLoading ? '...' : (pricingInfo?.price_euros || '49,00 €')}
                    </div>
                    <div className="text-sm text-gray-500">inkl. 19% MwSt.</div>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center text-green-700">
                    <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="font-medium">Professionelle Erstellung durch zertifizierte Energieberater</span>
                  </div>
                  <div className="flex items-center text-green-700 mt-2">
                    <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Lieferung innerhalb von 2-3 Werktagen als PDF</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Informational section about the certificate ordering process */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-green-900 mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Was passiert nach dem Kauf?
              </h3>
              <div className="space-y-3 text-green-800">
                <div className="flex items-start">
                  <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Sie erhalten eine Bestätigungs-E-Mail mit allen Details</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Ihr Energieausweis wird innerhalb von 2-3 Werktagen erstellt</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span>Sie werden per E-Mail benachrichtigt, sobald der Ausweis verfügbar ist</span>
                </div>
                <div className="flex items-start">
                  <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <span>Der fertige Ausweis wird Ihnen als PDF zugesendet</span>
                </div>
              </div>
            </div>
          </div>

          {/* Legal Compliance Section - Only show if not in payment status */}
          {!isPaymentStatus && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 id="legal-consent-heading" className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Rechtliche Bestimmungen
                </h3>
                <p className="text-blue-800 mb-4">
                  Bitte bestätigen Sie, dass Sie die folgenden rechtlichen Bestimmungen gelesen haben und diesen zustimmen:
                </p>

              <div className="space-y-5" role="group" aria-labelledby="legal-consent-heading">
                {/* AGB Checkbox */}
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="agb-consent"
                    checked={legalConsent.agb}
                    onChange={(e) => setLegalConsent(prev => ({ ...prev, agb: e.target.checked }))}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                    aria-describedby="agb-description"
                  />
                  <label htmlFor="agb-consent" className="ml-3 text-sm text-blue-800 leading-5">
                    <span className="font-medium">
                      Ich habe die{' '}
                      <Link
                        to="/legal/agb"
                        target="_blank"
                        className="text-blue-600 hover:text-blue-800 underline font-medium"
                        aria-label="Allgemeine Geschäftsbedingungen in neuem Tab öffnen"
                      >
                        Allgemeinen Geschäftsbedingungen (AGB)
                      </Link>
                      {' '}gelesen und stimme diesen zu.
                    </span>
                    <span className="text-red-600 ml-1">*</span>
                  </label>
                </div>

                {/* Datenschutz Checkbox */}
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="datenschutz-consent"
                    checked={legalConsent.datenschutz}
                    onChange={(e) => setLegalConsent(prev => ({ ...prev, datenschutz: e.target.checked }))}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                    aria-describedby="datenschutz-description"
                  />
                  <label htmlFor="datenschutz-consent" className="ml-3 text-sm text-blue-800 leading-5">
                    <span className="font-medium">
                      Ich habe die{' '}
                      <Link
                        to="/legal/datenschutz"
                        target="_blank"
                        className="text-blue-600 hover:text-blue-800 underline font-medium"
                        aria-label="Datenschutzerklärung in neuem Tab öffnen"
                      >
                        Datenschutzerklärung
                      </Link>
                      {' '}gelesen und stimme der Verarbeitung meiner Daten zu.
                    </span>
                    <span className="text-red-600 ml-1">*</span>
                  </label>
                </div>

                {/* Widerrufsrecht Checkbox */}
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="widerruf-consent"
                    checked={legalConsent.widerruf}
                    onChange={(e) => setLegalConsent(prev => ({ ...prev, widerruf: e.target.checked }))}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                    aria-describedby="widerruf-description"
                  />
                  <label htmlFor="widerruf-consent" className="ml-3 text-sm text-blue-800 leading-5">
                    <span className="font-medium">
                      Ich habe die{' '}
                      <Link
                        to="/legal/widerruf"
                        target="_blank"
                        className="text-blue-600 hover:text-blue-800 underline font-medium"
                        aria-label="Widerrufsbelehrung in neuem Tab öffnen"
                      >
                        Widerrufsbelehrung
                      </Link>
                      {' '}gelesen und stimme der sofortigen Ausführung der Dienstleistung zu. Mir ist bewusst, dass dadurch mein Widerrufsrecht erlischt.
                    </span>
                    <span className="text-red-600 ml-1">*</span>
                  </label>
                </div>

                {/* Data Accuracy Confirmation Checkbox */}
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="data-accuracy-consent"
                    checked={legalConsent.dataAccuracy}
                    onChange={(e) => setLegalConsent(prev => ({ ...prev, dataAccuracy: e.target.checked }))}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded flex-shrink-0"
                    aria-describedby="data-accuracy-description"
                  />
                  <label htmlFor="data-accuracy-consent" className="ml-3 text-sm text-blue-800 leading-5">
                    <span className="font-medium">
                      Ich bestätige, dass die von mir in diesem Erfassungsbogen gemachten Angaben nach bestem Wissen und Gewissen erfolgten und die Daten somit vollständig und inhaltlich korrekt sind.
                    </span>
                    <span className="text-red-600 ml-1">*</span>
                  </label>
                </div>
              </div>

              <div className="mt-6 pt-4 border-t border-blue-200">
                <p className="text-xs text-blue-700 flex items-start">
                  <svg className="w-3 h-3 text-red-600 mt-0.5 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                  <span>
                    <span className="text-red-600 font-medium">*</span> Pflichtfelder - Alle Bestimmungen müssen akzeptiert werden, um fortzufahren.
                  </span>
                </p>
              </div>
            </div>
          </div>
          )}

          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleBackNavigation}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
                >
                  Zurück
                </button>
                <p className="text-gray-600">
                  Letzte Aktualisierung: {new Date(energieausweisData.updated_at).toLocaleString('de-DE')}
                </p>
              </div>
              {/* Conditional button based on payment status */}
              {isPaid ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center text-green-600">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-lg font-medium">Bezahlt & Abgeschlossen</span>
                  </div>
                </div>
              ) : isPaymentInProgress ? (
                <div className="flex items-center space-x-3">
                  <div className="flex items-center text-blue-600">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-lg font-medium">Zahlung läuft...</span>
                  </div>
                </div>
              ) : (
                <button
                  className={`px-6 py-3 rounded-lg transition-colors text-lg font-medium ${
                    isProcessingPayment || !legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy
                      ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                      : isPaymentFailed
                        ? 'bg-orange-600 text-white hover:bg-orange-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                  onClick={handleCheckout}
                  disabled={isProcessingPayment || !energieausweisData || !legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy}
                >
                  {isProcessingPayment
                    ? 'Zahlung wird verarbeitet...'
                    : (!legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy)
                      ? 'Bitte stimmen Sie allen rechtlichen Bestimmungen zu'
                      : isPaymentFailed
                        ? 'Zahlung erneut versuchen'
                        : isAnonymous
                          ? 'Weiter zur Zahlung'
                          : 'Energieausweis kaufen'
                  }
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Account Conversion Modal for Anonymous Users */}
      {isAnonymous && userEmail && showAccountConversion && (
        <AccountConversionModal
          isOpen={showAccountConversion}
          onClose={handleAccountConversionClose}
          onSuccess={handleAccountConversionSuccess}
          userEmail={userEmail}
        />
      )}
    </div>
  );
};
