import { useState, useMemo, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { listCertificateFiles, deleteFile, type StorageFileInfo } from '../utils/fileUtils';

/**
 * Hook for managing certificate files using directory-based approach
 * This replaces the database-dependent file management
 */
export const useCertificateFiles = (certificateId: string | null) => {
  const queryClient = useQueryClient();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Only log when certificateId actually changes, not on every hook call
  const prevCertificateIdRef = useMemo(() => ({ current: certificateId }), []);
  useEffect(() => {
    if (prevCertificateIdRef.current !== certificateId) {
      console.log('📁 useCertificateFiles certificateId changed:', certificateId);
      prevCertificateIdRef.current = certificateId;
    }
  }, [certificateId]);

  // Get current user with enhanced caching to prevent refetches
  const { data: user } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      return user;
    },
    staleTime: 10 * 60 * 1000, // Increased to 10 minutes
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnMount: false, // Only refetch if stale
    refetchOnReconnect: false, // Don't refetch on network reconnect
  });

  // Query for certificate files with optimized caching strategy to reduce re-renders
  const {
    data: files = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['certificate-files', user?.id, certificateId],
    queryFn: async () => {
      if (!user?.id || !certificateId) return [];
      return await listCertificateFiles(user.id, certificateId);
    },
    enabled: !!user?.id && !!certificateId,
    staleTime: 2 * 60 * 1000, // Increased to 2 minutes to reduce unnecessary refetches
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    refetchOnWindowFocus: false, // Disable to prevent excessive refetches
    refetchOnMount: false, // Only refetch if data is stale
    retry: 2, // Reduce retry attempts
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Shorter max delay
  });

  // Group files by field name for easier access - memoized to prevent recalculation
  const filesByField = useMemo(() => {
    return files.reduce((acc, file) => {
      if (!acc[file.fieldName]) {
        acc[file.fieldName] = [];
      }
      acc[file.fieldName].push(file);
      return acc;
    }, {} as Record<string, StorageFileInfo[]>);
  }, [files]);

  // Get files for specific field
  const getFilesForField = (fieldName: string): StorageFileInfo[] => {
    return filesByField[fieldName] || [];
  };

  // Delete a file
  const deleteFileFromStorage = async (filePath: string): Promise<boolean> => {
    setIsDeleting(filePath);
    try {
      const success = await deleteFile(filePath);
      if (success) {
        // Invalidate and refetch the files query
        await queryClient.invalidateQueries({
          queryKey: ['certificate-files', user?.id, certificateId]
        });
        await refetch();
      }
      return success;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    } finally {
      setIsDeleting(null);
    }
  };

  // Check if a filename already exists in the certificate
  // This function now includes additional safety checks
  const checkFileExists = (fileName: string): boolean => {
    if (!fileName || !files || files.length === 0) return false;

    // Check both original name and the full storage name for safety
    return files.some(file =>
      file.originalName === fileName ||
      file.name === `${file.fieldName}_${fileName}`
    );
  };

  // Enhanced version that can force a fresh check from storage
  const checkFileExistsWithRefresh = async (fileName: string): Promise<boolean> => {
    if (!user?.id || !certificateId || !fileName) return false;

    try {
      // Force a fresh query to storage
      const freshFiles = await listCertificateFiles(user.id, certificateId);
      return freshFiles.some(file =>
        file.originalName === fileName ||
        file.name === `${file.fieldName}_${fileName}`
      );
    } catch (error) {
      console.warn('Error checking file existence with refresh:', error);
      // Fallback to cached version
      return checkFileExists(fileName);
    }
  };

  // Get total file count and size for validation
  const getFileStats = () => {
    const totalFiles = files.length;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    
    const statsByField = Object.keys(filesByField).reduce((acc, fieldName) => {
      const fieldFiles = filesByField[fieldName];
      acc[fieldName] = {
        count: fieldFiles.length,
        size: fieldFiles.reduce((sum, file) => sum + file.size, 0)
      };
      return acc;
    }, {} as Record<string, { count: number; size: number }>);

    return {
      totalFiles,
      totalSize,
      byField: statsByField
    };
  };

  // Refresh files (useful after uploads)
  const refreshFiles = async () => {
    await queryClient.invalidateQueries({
      queryKey: ['certificate-files', user?.id, certificateId]
    });
    await refetch();
  };

  return {
    files,
    filesByField,
    isLoading,
    error,
    isDeleting,
    getFilesForField,
    deleteFileFromStorage,
    checkFileExists,
    checkFileExistsWithRefresh,
    getFileStats,
    refreshFiles
  };
};
